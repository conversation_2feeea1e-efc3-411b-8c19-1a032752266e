# 敏感词检测模块使用说明

## 概述

本模块基于 `sensitive-word` 库实现了完整的敏感词检测功能，支持敏感词的增删改查以及文本内容的敏感词检测和替换。

## 功能特性

### 1. 敏感词管理
- ✅ 敏感词的增删改查
- ✅ 敏感词分类管理
- ✅ 敏感词级别设置（低级、中级、高级、严重）
- ✅ 敏感词处理动作（拦截、替换、警告）
- ✅ 批量导入/删除敏感词
- ✅ 敏感词启用/禁用状态管理

### 2. 敏感词检测
- ✅ 文本敏感词检测
- ✅ 敏感词自动替换
- ✅ 详细检测结果（包含位置、级别等信息）
- ✅ 支持多种检测模式（忽略大小写、全角半角等）
- ✅ 缓存机制提升检测性能

### 3. API接口
- ✅ RESTful API设计
- ✅ Swagger文档支持
- ✅ 统一响应格式
- ✅ 参数验证

## 数据库表结构

### 敏感词分类表 (t_sensitive_word_category)
```sql
- id: 主键ID
- name: 分类名称
- description: 分类描述
- status: 状态（0-禁用，1-启用）
- sequence: 排序序号
- use_count: 使用次数
- creator/updater: 创建者/更新者
- create_time/update_time: 创建/更新时间
- deleted: 删除标记
```

### 敏感词表 (t_sensitive_word)
```sql
- id: 主键ID
- word: 敏感词内容
- category_id: 分类ID
- level: 敏感词级别（1-低级，2-中级，3-高级，4-严重）
- action: 处理动作（1-拦截，2-替换，3-警告）
- replacement: 替换内容
- status: 状态（0-禁用，1-启用）
- remark: 备注
- use_count: 使用次数
- creator/updater: 创建者/更新者
- create_time/update_time: 创建/更新时间
- deleted: 删除标记
```

## API接口说明

### 敏感词分类管理

#### 1. 创建敏感词分类
```http
POST /api/v1/sensitive-word-category
Content-Type: application/json

{
    "name": "测试分类",
    "description": "测试分类描述",
    "status": 1,
    "sequence": 1
}
```

#### 2. 获取分类列表
```http
GET /api/v1/sensitive-word-category/list
```

#### 3. 更新分类状态
```http
PUT /api/v1/sensitive-word-category/{id}/status/{status}
```

### 敏感词管理

#### 1. 创建敏感词
```http
POST /api/v1/sensitive-word
Content-Type: application/json

{
    "word": "测试敏感词",
    "categoryId": 1,
    "level": 2,
    "action": 2,
    "replacement": "***",
    "status": 1,
    "remark": "测试用敏感词"
}
```

#### 2. 分页查询敏感词
```http
POST /api/v1/sensitive-word/page
Content-Type: application/json

{
    "pageNum": 1,
    "pageSize": 10,
    "filter": {
        "keyword": "测试",
        "categoryId": 1,
        "status": 1
    }
}
```

#### 3. 批量删除敏感词
```http
POST /api/v1/sensitive-word/batch/delete
Content-Type: application/json

[1, 2, 3]
```

### 敏感词检测

#### 1. 检测文本敏感词
```http
POST /api/v1/sensitive-word/check
Content-Type: application/json

{
    "content": "这是一段包含测试敏感词的文本",
    "detailed": true,
    "autoReplace": false,
    "replaceChar": "*"
}
```

响应示例：
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "hasSensitiveWord": true,
        "sensitiveWords": ["测试敏感词"],
        "originalText": "这是一段包含测试敏感词的文本",
        "processedText": "这是一段包含测试敏感词的文本",
        "details": [
            {
                "word": "测试敏感词",
                "position": 6,
                "level": 2,
                "action": 2,
                "replacement": "***",
                "categoryName": "测试分类"
            }
        ]
    }
}
```

#### 2. 替换文本敏感词
```http
POST /api/v1/sensitive-word/replace
Content-Type: application/json

{
    "content": "这是一段包含测试敏感词的文本",
    "replaceChar": "*"
}
```

## 工具类使用

### SensitiveWordUtil 静态方法

```java
// 检测是否包含敏感词
boolean hasSensitive = SensitiveWordUtil.containsSensitiveWord("测试文本");

// 获取敏感词列表
List<String> words = SensitiveWordUtil.findSensitiveWords("测试文本");

// 替换敏感词
String cleanText = SensitiveWordUtil.replaceText("测试文本", "*");

// 详细检测
SensitiveWordCheckResultVO result = SensitiveWordUtil.checkText("测试文本");

// 验证文本是否有效（无敏感词）
boolean isValid = SensitiveWordUtil.isTextValid("测试文本");
```

## 配置说明

在 `application.yml` 中可以配置敏感词检测相关参数：

```yaml
sensitive-word:
  enabled: true                    # 是否启用敏感词检测
  default-replace-char: "*"        # 默认替换字符
  ignore-case: true               # 是否忽略大小写
  ignore-width: true              # 是否忽略全角半角
  ignore-num-style: true          # 是否忽略数字样式
  ignore-chinese-style: true      # 是否忽略中文样式
  ignore-english-style: true      # 是否忽略英文样式
  ignore-repeat: false            # 是否忽略重复字符
  enable-num-check: true          # 是否启用数字检测
  enable-email-check: true        # 是否启用邮箱检测
  enable-url-check: true          # 是否启用URL检测
  enable-word-check: true         # 是否启用词汇检测
  num-check-len: 8               # 数字检测长度
  cache-refresh-interval: 60      # 缓存刷新间隔（分钟）
  enable-cache: true             # 是否启用缓存
  cache-expire-time: 30          # 缓存过期时间（分钟）
```

## 部署说明

1. 执行数据库脚本 `sensitive_word_tables.sql` 创建相关表
2. 确保已添加 `sensitive-word` 依赖
3. 启动应用，系统会自动初始化敏感词库
4. 通过API接口管理敏感词和进行检测

## 注意事项

1. 敏感词库会在应用启动时自动加载到内存中
2. 当敏感词数据发生变化时，需要调用刷新缓存接口更新内存中的数据
3. 建议定期清理无用的敏感词以提升检测性能
4. 敏感词检测支持多种模式，可根据业务需求调整配置
5. 使用批量操作时注意数据量，避免影响系统性能
