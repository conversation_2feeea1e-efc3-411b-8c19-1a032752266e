import com.github.houbb.sensitive.word.core.SensitiveWordHelper;
import java.util.List;

/**
 * Sensitive Word Quick Test
 */
public class SensitiveWordQuickTest {

    public static void main(String[] args) {
        System.out.println("=== Sensitive Word Detection Test ===");

        // Test text with Chinese
        String text = "这是一段测试文本，包含一些内容";

        try {
            // Check if contains sensitive words
            boolean contains = SensitiveWordHelper.contains(text);
            System.out.println("Text: " + text);
            System.out.println("Contains sensitive words: " + contains);

            // Find all sensitive words
            List<String> words = SensitiveWordHelper.findAll(text);
            System.out.println("Detected sensitive words: " + words);

            // Replace sensitive words
            String replaced = SensitiveWordHelper.replace(text, '*');
            System.out.println("Replaced text: " + replaced);

            System.out.println("\n=== Sensitive word library loaded successfully ===");

        } catch (Exception e) {
            System.err.println("Sensitive word detection error: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
