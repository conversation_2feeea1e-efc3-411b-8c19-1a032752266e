#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
色情低俗敏感词SQL生成脚本
读取 sw_色情.txt 文件，生成标准的SQL插入语句
"""

import re
import os

def clean_word(word):
    """清理敏感词，去除多余字符"""
    word = word.strip()
    word = word.rstrip(',')
    word = word.rstrip('.')
    word = word.rstrip('。')
    return word

def determine_level_and_action(word):
    """根据色情敏感词内容确定级别和处理动作"""
    # 严重级别关键词 (level=4, action=1-拦截)
    severe_keywords = [
        '强暴', '强奸', '轮奸', '性虐', '兽交', '乱伦', '幼女', '幼童', 
        '儿童色情', '未成年', '性奴', '人兽', '变态', '虐待'
    ]
    
    # 高级别关键词 (level=3, action=1-拦截)
    high_keywords = [
        '性交', '做爱', '操逼', '日逼', '插逼', '干逼', '舔逼', '口交', 
        '肛交', '群交', '乱交', '淫荡', '骚逼', '鸡巴', '阴茎', '阴道',
        '精液', '射精', '高潮', '自慰', '手淫', '性器', '生殖器'
    ]
    
    # 中级别关键词 (level=2, action=2-替换)
    medium_keywords = [
        '色情', '黄色', '成人', '裸体', '裸照', '艳照', '三级', 'av', 
        '情色', '性感', '诱惑', '挑逗', '调情', '风骚', '妩媚'
    ]
    
    # 检查是否包含严重级别关键词
    for keyword in severe_keywords:
        if keyword in word:
            return 4, 1, None  # 严重级别，拦截
    
    # 检查是否包含高级别关键词
    for keyword in high_keywords:
        if keyword in word:
            return 3, 1, NULL  # 高级别，拦截
    
    # 检查是否包含中级别关键词
    for keyword in medium_keywords:
        if keyword in word:
            return 2, 2, '***'  # 中级别，替换
    
    # 检查是否包含明显的色情词汇
    sexual_indicators = [
        '逼', '屄', '鸡', '屌', '淫', '骚', '操', '干', '日', '插', '舔', 
        '吸', '摸', '抚', '爱液', '精', '液', '穴', '洞', '棒', '根'
    ]
    
    for indicator in sexual_indicators:
        if indicator in word:
            return 3, 1, None  # 高级别，拦截
    
    # 默认中级别，替换
    return 2, 2, '***'

def generate_sql():
    """生成SQL插入语句"""
    input_file = 'agent/src/main/resources/sw_色情.txt'
    output_file = 'agent/src/main/resources/sexual_sensitive_words_complete.sql'
    
    if not os.path.exists(input_file):
        print(f"输入文件不存在: {input_file}")
        return
    
    words = []
    
    # 读取敏感词文件
    with open(input_file, 'r', encoding='utf-8') as f:
        for line in f:
            word = clean_word(line)
            if word and word != '':
                words.append(word)
    
    print(f"读取到 {len(words)} 个色情敏感词")
    
    # 生成SQL语句
    sql_statements = []
    sql_statements.append("-- 色情低俗敏感词完整插入SQL语句")
    sql_statements.append("-- 基于 sw_色情.txt 文件自动生成")
    sql_statements.append("-- 分类ID: 2 (色情低俗)")
    sql_statements.append("-- 级别说明: 1-低级，2-中级，3-高级，4-严重")
    sql_statements.append("-- 动作说明: 1-拦截，2-替换，3-警告")
    sql_statements.append("")
    
    # 分批插入，每50个词一批
    batch_size = 50
    for i in range(0, len(words), batch_size):
        batch = words[i:i+batch_size]
        
        sql_statements.append(f"-- 批次 {i//batch_size + 1}: 插入第 {i+1} 到第 {min(i+batch_size, len(words))} 个敏感词")
        sql_statements.append("INSERT INTO t_sensitive_word (word, category_id, level, action, replacement, status, remark, creator, updater, create_time, update_time, deleted, use_count) VALUES")
        
        values = []
        for j, word in enumerate(batch):
            level, action, replacement = determine_level_and_action(word)
            
            # 转义单引号
            escaped_word = word.replace("'", "''")
            
            # 构建replacement字段
            replacement_str = f"'{replacement}'" if replacement else "NULL"
            
            # 确定备注
            if level == 4:
                remark = "色情敏感词-严重级别"
            elif level == 3:
                remark = "色情敏感词-高级别"
            elif level == 2:
                remark = "色情敏感词-中级别"
            else:
                remark = "色情敏感词-低级别"
            
            value = f"('{escaped_word}', 2, {level}, {action}, {replacement_str}, 1, '{remark}', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 0, 0)"
            values.append(value)
        
        sql_statements.append(",\n".join(values) + ";")
        sql_statements.append("")
    
    # 添加统计信息
    level_counts = {1: 0, 2: 0, 3: 0, 4: 0}
    for word in words:
        level, _, _ = determine_level_and_action(word)
        level_counts[level] += 1
    
    sql_statements.append("-- 统计信息:")
    sql_statements.append(f"-- 总计敏感词数量: {len(words)} 个")
    sql_statements.append(f"-- 低级别(level=1): {level_counts[1]} 个")
    sql_statements.append(f"-- 中级别(level=2): {level_counts[2]} 个")
    sql_statements.append(f"-- 高级别(level=3): {level_counts[3]} 个")
    sql_statements.append(f"-- 严重级别(level=4): {level_counts[4]} 个")
    
    # 写入输出文件
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write('\n'.join(sql_statements))
    
    print(f"SQL文件已生成: {output_file}")
    print(f"统计信息:")
    print(f"  总计: {len(words)} 个敏感词")
    print(f"  低级别: {level_counts[1]} 个")
    print(f"  中级别: {level_counts[2]} 个")
    print(f"  高级别: {level_counts[3]} 个")
    print(f"  严重级别: {level_counts[4]} 个")

if __name__ == "__main__":
    generate_sql()
