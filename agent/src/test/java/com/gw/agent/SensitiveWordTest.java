package com.gw.agent;

import com.github.houbb.sensitive.word.bs.SensitiveWordBs;
import com.github.houbb.sensitive.word.core.SensitiveWordHelper;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.List;

/**
 * 敏感词功能测试
 */
public class SensitiveWordTest {
    
    @Test
    public void testSensitiveWordBasic() {
        // 测试基本的敏感词检测功能
        String text = "这是一段包含敏感词的文本";
        
        // 使用默认的敏感词库
        boolean contains = SensitiveWordHelper.contains(text);
        System.out.println("是否包含敏感词: " + contains);
        
        List<String> words = SensitiveWordHelper.findAll(text);
        System.out.println("检测到的敏感词: " + words);
        
        String replaced = SensitiveWordHelper.replace(text, '*');
        System.out.println("替换后的文本: " + replaced);
    }
    
    @Test
    public void testCustomSensitiveWords() {
        // 测试自定义敏感词
        List<String> customWords = Arrays.asList("测试敏感词", "违禁词汇", "不当内容");
        
        SensitiveWordBs sensitiveWordBs = SensitiveWordBs.newInstance()
            .wordDeny(() -> customWords)
            .ignoreCase(true)
            .ignoreWidth(true)
            .init();
        
        String text = "这段文本包含测试敏感词和其他内容";
        
        boolean contains = sensitiveWordBs.contains(text);
        System.out.println("是否包含自定义敏感词: " + contains);
        
        List<String> foundWords = sensitiveWordBs.findAll(text);
        System.out.println("检测到的自定义敏感词: " + foundWords);
        
        String replaced = sensitiveWordBs.replace(text, "*");
        System.out.println("替换后的文本: " + replaced);
    }
}
