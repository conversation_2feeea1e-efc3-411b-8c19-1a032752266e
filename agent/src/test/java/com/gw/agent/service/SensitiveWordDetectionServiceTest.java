package com.gw.agent.service;

import com.gw.agent.vo.SensitiveWordCheckResultVO;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 敏感词检测服务测试类
 */
@SpringBootTest
@ActiveProfiles("test")
public class SensitiveWordDetectionServiceTest {
    
    @Autowired
    private SensitiveWordDetectionService detectionService;
    
    @Test
    public void testContainsSensitiveWord() {
        // 测试包含敏感词的文本
        String content1 = "这是一段包含测试敏感词1的文本";
        assertTrue(detectionService.containsSensitiveWord(content1));
        
        // 测试不包含敏感词的文本
        String content2 = "这是一段正常的文本内容";
        assertFalse(detectionService.containsSensitiveWord(content2));
        
        // 测试空文本
        assertFalse(detectionService.containsSensitiveWord(""));
        assertFalse(detectionService.containsSensitiveWord(null));
    }
    
    @Test
    public void testFindSensitiveWords() {
        String content = "这段文本包含测试敏感词1和测试敏感词2";
        List<String> words = detectionService.findSensitiveWords(content);
        
        assertNotNull(words);
        assertTrue(words.size() > 0);
        assertTrue(words.contains("测试敏感词1"));
        assertTrue(words.contains("测试敏感词2"));
    }
    
    @Test
    public void testReplaceText() {
        String content = "这段文本包含测试敏感词1";
        String result = detectionService.replaceText(content, "*");
        
        assertNotNull(result);
        assertFalse(result.contains("测试敏感词1"));
        assertTrue(result.contains("*"));
    }
    
    @Test
    public void testCheckText() {
        String content = "这段文本包含测试敏感词1";
        SensitiveWordCheckResultVO result = detectionService.checkText(content, true);
        
        assertNotNull(result);
        assertTrue(result.getHasSensitiveWord());
        assertNotNull(result.getSensitiveWords());
        assertTrue(result.getSensitiveWords().size() > 0);
        assertEquals(content, result.getOriginalText());
        assertNotNull(result.getDetails());
    }
    
    @Test
    public void testCheckTextWithoutDetails() {
        String content = "这段文本包含测试敏感词1";
        SensitiveWordCheckResultVO result = detectionService.checkText(content, false);
        
        assertNotNull(result);
        assertTrue(result.getHasSensitiveWord());
        assertNotNull(result.getSensitiveWords());
        assertTrue(result.getSensitiveWords().size() > 0);
        assertEquals(content, result.getOriginalText());
    }
    
    @Test
    public void testCheckCleanText() {
        String content = "这是一段完全正常的文本内容";
        SensitiveWordCheckResultVO result = detectionService.checkText(content, true);
        
        assertNotNull(result);
        assertFalse(result.getHasSensitiveWord());
        assertNotNull(result.getSensitiveWords());
        assertEquals(0, result.getSensitiveWords().size());
        assertEquals(content, result.getOriginalText());
    }
}
