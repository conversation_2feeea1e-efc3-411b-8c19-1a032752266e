package com.gw.agent.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 敏感词配置类
 */
@Configuration
@ConfigurationProperties(prefix = "sensitive-word")
@Data
public class SensitiveWordConfig {
    
    /**
     * 是否启用敏感词检测
     */
    private Boolean enabled = true;
    
    /**
     * 默认替换字符
     */
    private String defaultReplaceChar = "*";
    
    /**
     * 是否忽略大小写
     */
    private Boolean ignoreCase = true;
    
    /**
     * 是否忽略全角半角
     */
    private Boolean ignoreWidth = true;
    
    /**
     * 是否忽略数字样式
     */
    private Boolean ignoreNumStyle = true;
    
    /**
     * 是否忽略中文样式
     */
    private Boolean ignoreChineseStyle = true;
    
    /**
     * 是否忽略英文样式
     */
    private Boolean ignoreEnglishStyle = true;
    
    /**
     * 是否忽略重复字符
     */
    private Boolean ignoreRepeat = false;
    
    /**
     * 是否启用数字检测
     */
    private Boolean enableNumCheck = true;
    
    /**
     * 是否启用邮箱检测
     */
    private Boolean enableEmailCheck = true;
    
    /**
     * 是否启用URL检测
     */
    private Boolean enableUrlCheck = true;
    
    /**
     * 是否启用词汇检测
     */
    private Boolean enableWordCheck = true;
    
    /**
     * 数字检测长度
     */
    private Integer numCheckLen = 8;
    
    /**
     * 缓存刷新间隔（分钟）
     */
    private Integer cacheRefreshInterval = 60;
    
    /**
     * 是否启用缓存
     */
    private Boolean enableCache = true;
    
    /**
     * 缓存过期时间（分钟）
     */
    private Integer cacheExpireTime = 30;
}
