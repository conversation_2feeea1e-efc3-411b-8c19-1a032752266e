package com.gw.agent.mapper.sql;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gw.agent.entity.SensitiveWordCategoryEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;
import java.util.Optional;

/**
 * 敏感词分类数据访问接口
 */
@Mapper
public interface SensitiveWordCategoryMapper extends BaseMapper<SensitiveWordCategoryEntity> {
    
    /**
     * 查询所有启用的分类
     */
    @Select("SELECT * FROM t_sensitive_word_category WHERE deleted = 0 AND status = 1 ORDER BY sequence ASC, create_time DESC")
    List<SensitiveWordCategoryEntity> findAllEnabled();
    
    /**
     * 查询所有分类
     */
    @Select("SELECT * FROM t_sensitive_word_category WHERE deleted = 0 ORDER BY sequence ASC, create_time DESC")
    List<SensitiveWordCategoryEntity> findAll();
    
    /**
     * 根据名称查询分类
     */
    @Select("SELECT * FROM t_sensitive_word_category WHERE deleted = 0 AND name = #{name}")
    Optional<SensitiveWordCategoryEntity> findByName(@Param("name") String name);
    
    /**
     * 根据ID查询分类
     */
    @Select("SELECT * FROM t_sensitive_word_category WHERE deleted = 0 AND id = #{id}")
    Optional<SensitiveWordCategoryEntity> findById(@Param("id") Long id);
    
    /**
     * 更新使用次数
     */
    @Update("UPDATE t_sensitive_word_category SET use_count = use_count + #{increment} WHERE id = #{id}")
    void updateUseCount(@Param("id") Long id, @Param("increment") Integer increment);
}
