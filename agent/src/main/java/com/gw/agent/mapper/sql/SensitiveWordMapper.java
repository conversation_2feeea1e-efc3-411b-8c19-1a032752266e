package com.gw.agent.mapper.sql;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gw.agent.dto.SensitiveWordQueryDTO;
import com.gw.agent.entity.SensitiveWordEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;
import java.util.Optional;

/**
 * 敏感词数据访问接口
 */
@Mapper
public interface SensitiveWordMapper extends BaseMapper<SensitiveWordEntity> {
    
    /**
     * 查询所有启用的敏感词
     */
    @Select("SELECT * FROM t_sensitive_word WHERE deleted = 0 AND status = 1")
    List<SensitiveWordEntity> findAllEnabled();
    
    /**
     * 查询所有敏感词
     */
    @Select("SELECT * FROM t_sensitive_word WHERE deleted = 0 ORDER BY create_time DESC")
    List<SensitiveWordEntity> findAll();
    
    /**
     * 根据词内容查询敏感词
     */
    @Select("SELECT * FROM t_sensitive_word WHERE deleted = 0 AND word = #{word}")
    Optional<SensitiveWordEntity> findByWord(@Param("word") String word);
    
    /**
     * 根据ID查询敏感词
     */
    @Select("SELECT * FROM t_sensitive_word WHERE deleted = 0 AND id = #{id}")
    Optional<SensitiveWordEntity> findById(@Param("id") Long id);
    
    /**
     * 根据分类ID查询敏感词
     */
    @Select("SELECT * FROM t_sensitive_word WHERE deleted = 0 AND category_id = #{categoryId} AND status = 1")
    List<SensitiveWordEntity> findByCategoryId(@Param("categoryId") Long categoryId);
    
    /**
     * 更新使用次数
     */
    @Update("UPDATE t_sensitive_word SET use_count = use_count + #{increment} WHERE id = #{id}")
    void updateUseCount(@Param("id") Long id, @Param("increment") Integer increment);
    
    /**
     * 分页查询敏感词
     */
    List<SensitiveWordEntity> page(@Param("query") SensitiveWordQueryDTO query);
    
    /**
     * 批量查询敏感词（用于检测）
     */
    @Select("SELECT * FROM t_sensitive_word WHERE deleted = 0 AND status = 1 AND word IN " +
            "<foreach collection='words' item='word' open='(' separator=',' close=')'>" +
            "#{word}" +
            "</foreach>")
    List<SensitiveWordEntity> findByWords(@Param("words") List<String> words);
}
