package com.gw.agent.example;

import com.gw.agent.service.SensitiveWordDetectionService;
import com.gw.agent.util.SensitiveWordUtil;
import com.gw.agent.vo.SensitiveWordCheckResultVO;
import com.gw.common.exception.BusinessException;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * 敏感词集成示例
 * 演示如何在业务代码中集成敏感词检测功能
 */
@Component
@RequiredArgsConstructor
@Log4j2
public class SensitiveWordIntegrationExample {
    
    private final SensitiveWordDetectionService detectionService;
    
    /**
     * 示例1：在用户评论中检测敏感词
     */
    public void validateUserComment(String comment) {
        if (!StringUtils.hasText(comment)) {
            throw new BusinessException("评论内容不能为空");
        }
        
        // 方式1：使用工具类检测
        if (SensitiveWordUtil.containsSensitiveWord(comment)) {
            List<String> sensitiveWords = SensitiveWordUtil.findSensitiveWords(comment);
            throw new BusinessException("评论包含敏感词：" + String.join(", ", sensitiveWords));
        }
        
        log.info("用户评论通过敏感词检测");
    }
    
    /**
     * 示例2：在智能体创建时检测敏感词并自动处理
     */
    public String processAgentContent(String content) {
        if (!StringUtils.hasText(content)) {
            return content;
        }
        
        // 方式2：使用服务类进行详细检测
        SensitiveWordCheckResultVO result = detectionService.checkText(content, true);
        
        if (result.getHasSensitiveWord()) {
            log.warn("检测到敏感词：{}", result.getSensitiveWords());
            
            // 根据敏感词级别决定处理方式
            boolean hasHighLevelWord = result.getDetails().stream()
                .anyMatch(detail -> detail.getLevel() >= 3); // 高级或严重级别
            
            if (hasHighLevelWord) {
                // 高级敏感词直接拒绝
                throw new BusinessException("内容包含严重敏感词，无法创建");
            } else {
                // 低级敏感词自动替换
                return detectionService.replaceText(content, "*");
            }
        }
        
        return content;
    }
    
    /**
     * 示例3：批量检测文本列表
     */
    public void batchValidateTexts(List<String> texts) {
        for (int i = 0; i < texts.size(); i++) {
            String text = texts.get(i);
            if (SensitiveWordUtil.containsSensitiveWord(text)) {
                List<String> words = SensitiveWordUtil.findSensitiveWords(text);
                log.warn("第{}条文本包含敏感词：{}", i + 1, words);
                
                // 可以选择替换或者标记
                texts.set(i, SensitiveWordUtil.replaceText(text));
            }
        }
    }
    
    /**
     * 示例4：在聊天消息中实时检测
     */
    public String processChatMessage(String message, String username) {
        if (!StringUtils.hasText(message)) {
            return message;
        }
        
        // 检测敏感词
        SensitiveWordCheckResultVO result = detectionService.checkText(message, true);
        
        if (result.getHasSensitiveWord()) {
            log.info("用户 {} 发送的消息包含敏感词：{}", username, result.getSensitiveWords());
            
            // 记录敏感词使用情况（已在检测服务中自动处理）
            
            // 根据配置决定是否允许发送
            boolean allowSend = result.getDetails().stream()
                .allMatch(detail -> detail.getLevel() <= 2); // 只允许低级和中级敏感词
            
            if (!allowSend) {
                throw new BusinessException("消息包含敏感词，无法发送");
            }
            
            // 自动替换敏感词
            return detectionService.replaceText(message, "*");
        }
        
        return message;
    }
    
    /**
     * 示例5：在文件上传时检测文件名
     */
    public void validateFileName(String fileName) {
        if (!StringUtils.hasText(fileName)) {
            throw new BusinessException("文件名不能为空");
        }
        
        if (SensitiveWordUtil.containsSensitiveWord(fileName)) {
            throw new BusinessException("文件名包含敏感词，请重新命名");
        }
    }
    
    /**
     * 示例6：在搜索关键词中检测敏感词
     */
    public String processSearchKeyword(String keyword) {
        if (!StringUtils.hasText(keyword)) {
            return keyword;
        }
        
        // 检测搜索关键词
        if (SensitiveWordUtil.containsSensitiveWord(keyword)) {
            List<String> sensitiveWords = SensitiveWordUtil.findSensitiveWords(keyword);
            log.warn("搜索关键词包含敏感词：{}", sensitiveWords);
            
            // 可以选择拒绝搜索或者替换关键词
            // 这里选择替换
            return SensitiveWordUtil.replaceText(keyword);
        }
        
        return keyword;
    }
    
    /**
     * 示例7：内容审核工作流
     */
    public ContentAuditResult auditContent(String content) {
        ContentAuditResult result = new ContentAuditResult();
        result.setOriginalContent(content);
        result.setPassed(true);
        
        if (!StringUtils.hasText(content)) {
            return result;
        }
        
        SensitiveWordCheckResultVO checkResult = detectionService.checkText(content, true);
        
        if (checkResult.getHasSensitiveWord()) {
            result.setPassed(false);
            result.setSensitiveWords(checkResult.getSensitiveWords());
            result.setProcessedContent(detectionService.replaceText(content, "*"));
            
            // 根据敏感词级别设置审核状态
            int maxLevel = checkResult.getDetails().stream()
                .mapToInt(SensitiveWordCheckResultVO.SensitiveWordDetailVO::getLevel)
                .max()
                .orElse(1);
            
            if (maxLevel >= 4) {
                result.setAuditStatus("REJECTED"); // 严重敏感词直接拒绝
            } else if (maxLevel >= 3) {
                result.setAuditStatus("MANUAL_REVIEW"); // 高级敏感词需要人工审核
            } else {
                result.setAuditStatus("AUTO_PROCESSED"); // 低级敏感词自动处理
            }
        } else {
            result.setAuditStatus("APPROVED");
            result.setProcessedContent(content);
        }
        
        return result;
    }
    
    /**
     * 内容审核结果类
     */
    public static class ContentAuditResult {
        private String originalContent;
        private String processedContent;
        private boolean passed;
        private List<String> sensitiveWords;
        private String auditStatus;
        
        // getters and setters
        public String getOriginalContent() { return originalContent; }
        public void setOriginalContent(String originalContent) { this.originalContent = originalContent; }
        
        public String getProcessedContent() { return processedContent; }
        public void setProcessedContent(String processedContent) { this.processedContent = processedContent; }
        
        public boolean isPassed() { return passed; }
        public void setPassed(boolean passed) { this.passed = passed; }
        
        public List<String> getSensitiveWords() { return sensitiveWords; }
        public void setSensitiveWords(List<String> sensitiveWords) { this.sensitiveWords = sensitiveWords; }
        
        public String getAuditStatus() { return auditStatus; }
        public void setAuditStatus(String auditStatus) { this.auditStatus = auditStatus; }
    }
}
