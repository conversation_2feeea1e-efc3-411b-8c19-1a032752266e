package com.gw.agent.constant;

/**
 * 敏感词相关常量
 */
public class SensitiveWordConstant {
    
    /**
     * 敏感词状态
     */
    public static final Integer STATUS_ENABLED = 1;  // 启用
    public static final Integer STATUS_DISABLED = 0; // 禁用
    
    /**
     * 敏感词分类状态
     */
    public static final Integer CATEGORY_STATUS_ENABLED = 1;  // 启用
    public static final Integer CATEGORY_STATUS_DISABLED = 0; // 禁用
    
    /**
     * 敏感词处理动作
     */
    public static final Integer ACTION_BLOCK = 1;    // 拦截
    public static final Integer ACTION_REPLACE = 2;  // 替换
    public static final Integer ACTION_WARN = 3;     // 警告
    
    /**
     * 敏感词级别
     */
    public static final Integer LEVEL_LOW = 1;       // 低级
    public static final Integer LEVEL_MEDIUM = 2;    // 中级
    public static final Integer LEVEL_HIGH = 3;      // 高级
    public static final Integer LEVEL_CRITICAL = 4;  // 严重
    
    /**
     * 默认替换字符
     */
    public static final String DEFAULT_REPLACE_CHAR = "*";
    
    /**
     * 缓存相关
     */
    public static final String SENSITIVE_WORD_CACHE = "sensitiveWord";
    public static final String SENSITIVE_WORD_CATEGORY_CACHE = "sensitiveWordCategory";
    public static final String SENSITIVE_WORD_PAGE_CACHE = "sensitiveWordPage";
    
    /**
     * 检测结果状态
     */
    public static final Integer CHECK_RESULT_CLEAN = 0;     // 无敏感词
    public static final Integer CHECK_RESULT_SENSITIVE = 1; // 包含敏感词
}
