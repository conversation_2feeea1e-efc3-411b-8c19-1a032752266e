package com.gw.agent.service;

import com.gw.agent.vo.SensitiveWordCheckResultVO;

import java.util.List;

/**
 * 敏感词检测服务接口
 */
public interface SensitiveWordDetectionService {
    
    /**
     * 检测文本中的敏感词
     * 
     * @param content 待检测的文本内容
     * @return 检测结果
     */
    SensitiveWordCheckResultVO checkText(String content);
    
    /**
     * 检测文本中的敏感词（详细模式）
     * 
     * @param content 待检测的文本内容
     * @param detailed 是否返回详细信息
     * @return 检测结果
     */
    SensitiveWordCheckResultVO checkText(String content, boolean detailed);
    
    /**
     * 检测并替换文本中的敏感词
     * 
     * @param content 待检测的文本内容
     * @param replaceChar 替换字符
     * @return 处理后的文本
     */
    String replaceText(String content, String replaceChar);
    
    /**
     * 检测文本是否包含敏感词
     * 
     * @param content 待检测的文本内容
     * @return 是否包含敏感词
     */
    boolean containsSensitiveWord(String content);
    
    /**
     * 获取文本中的所有敏感词
     * 
     * @param content 待检测的文本内容
     * @return 敏感词列表
     */
    List<String> findSensitiveWords(String content);
    
    /**
     * 刷新敏感词库缓存
     */
    void refreshCache();
    
    /**
     * 初始化敏感词库
     */
    void initSensitiveWords();
}
