package com.gw.agent.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.gw.agent.constant.SensitiveWordConstant;
import com.gw.agent.dto.SensitiveWordQueryDTO;
import com.gw.agent.entity.SensitiveWordEntity;
import com.gw.agent.mapper.sql.SensitiveWordMapper;
import com.gw.agent.service.SensitiveWordService;
import com.gw.common.exception.BusinessException;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 敏感词服务实现
 */
@Service
@RequiredArgsConstructor
@Log4j2
public class SensitiveWordServiceImpl implements SensitiveWordService {
    
    private final SensitiveWordMapper sensitiveWordMapper;
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = {SensitiveWordConstant.SENSITIVE_WORD_CACHE, SensitiveWordConstant.SENSITIVE_WORD_PAGE_CACHE}, allEntries = true)
    public void insert(SensitiveWordEntity entity) {
        log.info("开始创建敏感词: {}", entity.getWord());
        
        // 检查敏感词是否已存在
        if (sensitiveWordMapper.findByWord(entity.getWord()).isPresent()) {
            throw new BusinessException("敏感词已存在");
        }
        
        entity.setCreateTime(LocalDateTime.now());
        entity.setUpdateTime(LocalDateTime.now());
        sensitiveWordMapper.insert(entity);
        
        log.info("敏感词创建成功，ID: {}", entity.getId());
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = {SensitiveWordConstant.SENSITIVE_WORD_CACHE, SensitiveWordConstant.SENSITIVE_WORD_PAGE_CACHE}, allEntries = true)
    public void update(SensitiveWordEntity entity) {
        log.info("开始更新敏感词: {}", entity.getId());
        
        SensitiveWordEntity existing = findById(entity.getId());
        if (existing == null) {
            throw new BusinessException("敏感词不存在");
        }
        
        // 如果修改了词内容，检查新词是否已存在
        if (!existing.getWord().equals(entity.getWord())) {
            if (sensitiveWordMapper.findByWord(entity.getWord()).isPresent()) {
                throw new BusinessException("敏感词已存在");
            }
        }
        
        entity.setUpdateTime(LocalDateTime.now());
        sensitiveWordMapper.updateById(entity);
        
        log.info("敏感词更新成功，ID: {}", entity.getId());
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = {SensitiveWordConstant.SENSITIVE_WORD_CACHE, SensitiveWordConstant.SENSITIVE_WORD_PAGE_CACHE}, allEntries = true)
    public void delete(Long id) {
        log.info("开始删除敏感词: {}", id);
        
        SensitiveWordEntity entity = findById(id);
        if (entity == null) {
            throw new BusinessException("敏感词不存在");
        }
        
        sensitiveWordMapper.deleteById(id);
        log.info("敏感词删除成功，ID: {}", id);
    }
    
    @Override
    public SensitiveWordEntity findById(Long id) {
        return sensitiveWordMapper.findById(id).orElse(null);
    }
    
    @Override
    public SensitiveWordEntity findByWord(String word) {
        return sensitiveWordMapper.findByWord(word).orElse(null);
    }
    
    @Override
    @Cacheable(value = SensitiveWordConstant.SENSITIVE_WORD_CACHE, key = "'enabled'")
    public List<SensitiveWordEntity> findAllEnabled() {
        return sensitiveWordMapper.findAllEnabled();
    }
    
    @Override
    @Cacheable(value = SensitiveWordConstant.SENSITIVE_WORD_CACHE, key = "'all'")
    public List<SensitiveWordEntity> findAll() {
        return sensitiveWordMapper.findAll();
    }
    
    @Override
    public List<SensitiveWordEntity> findByCategoryId(Long categoryId) {
        return sensitiveWordMapper.findByCategoryId(categoryId);
    }
    
    @Override
    public PageInfo<SensitiveWordEntity> page(int pageNum, int pageSize, SensitiveWordQueryDTO query) {
        PageHelper.startPage(pageNum, pageSize);
        List<SensitiveWordEntity> list = sensitiveWordMapper.page(query);
        return new PageInfo<>(list);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateUseCount(Long id, Integer increment) {
        sensitiveWordMapper.updateUseCount(id, increment);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = {SensitiveWordConstant.SENSITIVE_WORD_CACHE, SensitiveWordConstant.SENSITIVE_WORD_PAGE_CACHE}, allEntries = true)
    public void batchImport(List<SensitiveWordEntity> words) {
        if (CollectionUtils.isEmpty(words)) {
            return;
        }
        
        log.info("开始批量导入敏感词，数量: {}", words.size());
        
        for (SensitiveWordEntity word : words) {
            try {
                // 检查是否已存在
                if (sensitiveWordMapper.findByWord(word.getWord()).isEmpty()) {
                    word.setCreateTime(LocalDateTime.now());
                    word.setUpdateTime(LocalDateTime.now());
                    sensitiveWordMapper.insert(word);
                }
            } catch (Exception e) {
                log.warn("导入敏感词失败: {}, 错误: {}", word.getWord(), e.getMessage());
            }
        }
        
        log.info("批量导入敏感词完成");
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = {SensitiveWordConstant.SENSITIVE_WORD_CACHE, SensitiveWordConstant.SENSITIVE_WORD_PAGE_CACHE}, allEntries = true)
    public void batchDelete(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        
        log.info("开始批量删除敏感词，数量: {}", ids.size());
        sensitiveWordMapper.deleteBatchIds(ids);
        log.info("批量删除敏感词完成");
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = {SensitiveWordConstant.SENSITIVE_WORD_CACHE, SensitiveWordConstant.SENSITIVE_WORD_PAGE_CACHE}, allEntries = true)
    public void updateStatus(Long id, Integer status) {
        log.info("开始更新敏感词状态: {}, 状态: {}", id, status);
        
        SensitiveWordEntity entity = findById(id);
        if (entity == null) {
            throw new BusinessException("敏感词不存在");
        }
        
        entity.setStatus(status);
        entity.setUpdateTime(LocalDateTime.now());
        sensitiveWordMapper.updateById(entity);
        
        log.info("敏感词状态更新成功，ID: {}", id);
    }
    
    @Override
    @CacheEvict(value = {SensitiveWordConstant.SENSITIVE_WORD_CACHE, SensitiveWordConstant.SENSITIVE_WORD_PAGE_CACHE}, allEntries = true)
    public void clearCache() {
        log.info("清除敏感词缓存");
    }
}
