package com.gw.agent.service.impl;

import com.gw.agent.constant.SensitiveWordConstant;
import com.gw.agent.entity.SensitiveWordCategoryEntity;
import com.gw.agent.mapper.sql.SensitiveWordCategoryMapper;
import com.gw.agent.service.SensitiveWordCategoryService;
import com.gw.common.exception.BusinessException;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 敏感词分类服务实现
 */
@Service
@RequiredArgsConstructor
@Log4j2
public class SensitiveWordCategoryServiceImpl implements SensitiveWordCategoryService {
    
    private final SensitiveWordCategoryMapper categoryMapper;
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = SensitiveWordConstant.SENSITIVE_WORD_CATEGORY_CACHE, allEntries = true)
    public void insert(SensitiveWordCategoryEntity entity) {
        log.info("开始创建敏感词分类: {}", entity.getName());
        
        // 检查分类名称是否已存在
        if (categoryMapper.findByName(entity.getName()).isPresent()) {
            throw new BusinessException("分类名称已存在");
        }
        
        entity.setCreateTime(LocalDateTime.now());
        entity.setUpdateTime(LocalDateTime.now());
        categoryMapper.insert(entity);
        
        log.info("敏感词分类创建成功，ID: {}", entity.getId());
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = SensitiveWordConstant.SENSITIVE_WORD_CATEGORY_CACHE, allEntries = true)
    public void update(SensitiveWordCategoryEntity entity) {
        log.info("开始更新敏感词分类: {}", entity.getId());
        
        SensitiveWordCategoryEntity existing = findById(entity.getId());
        if (existing == null) {
            throw new BusinessException("敏感词分类不存在");
        }
        
        // 如果修改了名称，检查新名称是否已存在
        if (!existing.getName().equals(entity.getName())) {
            if (categoryMapper.findByName(entity.getName()).isPresent()) {
                throw new BusinessException("分类名称已存在");
            }
        }
        
        entity.setUpdateTime(LocalDateTime.now());
        categoryMapper.updateById(entity);
        
        log.info("敏感词分类更新成功，ID: {}", entity.getId());
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = SensitiveWordConstant.SENSITIVE_WORD_CATEGORY_CACHE, allEntries = true)
    public void delete(Long id) {
        log.info("开始删除敏感词分类: {}", id);
        
        SensitiveWordCategoryEntity entity = findById(id);
        if (entity == null) {
            throw new BusinessException("敏感词分类不存在");
        }
        
        categoryMapper.deleteById(id);
        log.info("敏感词分类删除成功，ID: {}", id);
    }
    
    @Override
    public SensitiveWordCategoryEntity findById(Long id) {
        return categoryMapper.findById(id).orElse(null);
    }
    
    @Override
    public SensitiveWordCategoryEntity findByName(String name) {
        return categoryMapper.findByName(name).orElse(null);
    }
    
    @Override
    @Cacheable(value = SensitiveWordConstant.SENSITIVE_WORD_CATEGORY_CACHE, key = "'enabled'")
    public List<SensitiveWordCategoryEntity> findAllEnabled() {
        return categoryMapper.findAllEnabled();
    }
    
    @Override
    @Cacheable(value = SensitiveWordConstant.SENSITIVE_WORD_CATEGORY_CACHE, key = "'all'")
    public List<SensitiveWordCategoryEntity> findAll() {
        return categoryMapper.findAll();
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateUseCount(Long id, Integer increment) {
        categoryMapper.updateUseCount(id, increment);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = SensitiveWordConstant.SENSITIVE_WORD_CATEGORY_CACHE, allEntries = true)
    public void updateStatus(Long id, Integer status) {
        log.info("开始更新敏感词分类状态: {}, 状态: {}", id, status);
        
        SensitiveWordCategoryEntity entity = findById(id);
        if (entity == null) {
            throw new BusinessException("敏感词分类不存在");
        }
        
        entity.setStatus(status);
        entity.setUpdateTime(LocalDateTime.now());
        categoryMapper.updateById(entity);
        
        log.info("敏感词分类状态更新成功，ID: {}", id);
    }
    
    @Override
    @CacheEvict(value = SensitiveWordConstant.SENSITIVE_WORD_CATEGORY_CACHE, allEntries = true)
    public void clearCache() {
        log.info("清除敏感词分类缓存");
    }
}
