package com.gw.agent.service;

import com.gw.agent.entity.SensitiveWordCategoryEntity;

import java.util.List;

/**
 * 敏感词分类服务接口
 */
public interface SensitiveWordCategoryService {
    
    /**
     * 创建敏感词分类
     */
    void insert(SensitiveWordCategoryEntity entity);
    
    /**
     * 更新敏感词分类
     */
    void update(SensitiveWordCategoryEntity entity);
    
    /**
     * 删除敏感词分类
     */
    void delete(Long id);
    
    /**
     * 根据ID查询敏感词分类
     */
    SensitiveWordCategoryEntity findById(Long id);
    
    /**
     * 根据名称查询敏感词分类
     */
    SensitiveWordCategoryEntity findByName(String name);
    
    /**
     * 查询所有启用的敏感词分类
     */
    List<SensitiveWordCategoryEntity> findAllEnabled();
    
    /**
     * 查询所有敏感词分类
     */
    List<SensitiveWordCategoryEntity> findAll();
    
    /**
     * 更新使用次数
     */
    void updateUseCount(Long id, Integer increment);
    
    /**
     * 启用/禁用敏感词分类
     */
    void updateStatus(Long id, Integer status);
    
    /**
     * 清除敏感词分类缓存
     */
    void clearCache();
}
