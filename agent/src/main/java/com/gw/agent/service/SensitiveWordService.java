package com.gw.agent.service;

import com.github.pagehelper.PageInfo;
import com.gw.agent.dto.SensitiveWordQueryDTO;
import com.gw.agent.entity.SensitiveWordEntity;

import java.util.List;

/**
 * 敏感词服务接口
 */
public interface SensitiveWordService {
    
    /**
     * 创建敏感词
     */
    void insert(SensitiveWordEntity entity);
    
    /**
     * 更新敏感词
     */
    void update(SensitiveWordEntity entity);
    
    /**
     * 删除敏感词
     */
    void delete(Long id);
    
    /**
     * 根据ID查询敏感词
     */
    SensitiveWordEntity findById(Long id);
    
    /**
     * 根据词内容查询敏感词
     */
    SensitiveWordEntity findByWord(String word);
    
    /**
     * 查询所有启用的敏感词
     */
    List<SensitiveWordEntity> findAllEnabled();
    
    /**
     * 查询所有敏感词
     */
    List<SensitiveWordEntity> findAll();
    
    /**
     * 根据分类ID查询敏感词
     */
    List<SensitiveWordEntity> findByCategoryId(Long categoryId);
    
    /**
     * 分页查询敏感词
     */
    PageInfo<SensitiveWordEntity> page(int pageNum, int pageSize, SensitiveWordQueryDTO query);
    
    /**
     * 更新使用次数
     */
    void updateUseCount(Long id, Integer increment);
    
    /**
     * 批量导入敏感词
     */
    void batchImport(List<SensitiveWordEntity> words);
    
    /**
     * 批量删除敏感词
     */
    void batchDelete(List<Long> ids);
    
    /**
     * 启用/禁用敏感词
     */
    void updateStatus(Long id, Integer status);
    
    /**
     * 清除敏感词缓存
     */
    void clearCache();
}
