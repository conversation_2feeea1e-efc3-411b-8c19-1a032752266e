package com.gw.agent.service.impl;

import com.github.houbb.sensitive.word.api.IWordAllow;
import com.github.houbb.sensitive.word.api.IWordDeny;
import com.github.houbb.sensitive.word.bs.SensitiveWordBs;
import com.github.houbb.sensitive.word.core.SensitiveWordHelper;
import com.gw.agent.constant.SensitiveWordConstant;
import com.gw.agent.entity.SensitiveWordCategoryEntity;
import com.gw.agent.entity.SensitiveWordEntity;
import com.gw.agent.service.SensitiveWordCategoryService;
import com.gw.agent.service.SensitiveWordDetectionService;
import com.gw.agent.service.SensitiveWordService;
import com.gw.agent.vo.SensitiveWordCheckResultVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 敏感词检测服务实现
 */
@Service
@RequiredArgsConstructor
@Log4j2
public class SensitiveWordDetectionServiceImpl implements SensitiveWordDetectionService {
    
    private final SensitiveWordService sensitiveWordService;
    private final SensitiveWordCategoryService categoryService;
    
    private SensitiveWordBs sensitiveWordBs;
    private Map<String, SensitiveWordEntity> wordEntityMap = new HashMap<>();
    private Map<Long, SensitiveWordCategoryEntity> categoryEntityMap = new HashMap<>();
    
    @PostConstruct
    public void init() {
        initSensitiveWords();
    }
    
    @Override
    public SensitiveWordCheckResultVO checkText(String content) {
        return checkText(content, false);
    }
    
    @Override
    public SensitiveWordCheckResultVO checkText(String content, boolean detailed) {
        if (!StringUtils.hasText(content)) {
            return new SensitiveWordCheckResultVO(false, Collections.emptyList(), content, content, Collections.emptyList());
        }
        
        try {
            // 检测敏感词
            List<String> foundWords = sensitiveWordBs.findAll(content);
            boolean hasSensitiveWord = !CollectionUtils.isEmpty(foundWords);
            
            SensitiveWordCheckResultVO result = new SensitiveWordCheckResultVO();
            result.setHasSensitiveWord(hasSensitiveWord);
            result.setSensitiveWords(foundWords);
            result.setOriginalText(content);
            result.setProcessedText(content);
            
            if (detailed && hasSensitiveWord) {
                List<SensitiveWordCheckResultVO.SensitiveWordDetailVO> details = new ArrayList<>();
                
                for (String word : foundWords) {
                    SensitiveWordEntity entity = wordEntityMap.get(word);
                    if (entity != null) {
                        SensitiveWordCategoryEntity category = categoryEntityMap.get(entity.getCategoryId());
                        
                        SensitiveWordCheckResultVO.SensitiveWordDetailVO detail = 
                            new SensitiveWordCheckResultVO.SensitiveWordDetailVO();
                        detail.setWord(word);
                        detail.setPosition(content.indexOf(word));
                        detail.setLevel(entity.getLevel());
                        detail.setAction(entity.getAction());
                        detail.setReplacement(entity.getReplacement());
                        detail.setCategoryName(category != null ? category.getName() : "未知分类");
                        
                        details.add(detail);
                        
                        // 更新使用次数
                        try {
                            sensitiveWordService.updateUseCount(entity.getId(), 1);
                            if (category != null) {
                                categoryService.updateUseCount(category.getId(), 1);
                            }
                        } catch (Exception e) {
                            log.warn("更新敏感词使用次数失败: {}", e.getMessage());
                        }
                    }
                }
                
                result.setDetails(details);
            }
            
            return result;
            
        } catch (Exception e) {
            log.error("敏感词检测异常: {}", e.getMessage(), e);
            return new SensitiveWordCheckResultVO(false, Collections.emptyList(), content, content, Collections.emptyList());
        }
    }
    
    @Override
    public String replaceText(String content, String replaceChar) {
        if (!StringUtils.hasText(content)) {
            return content;
        }
        
        try {
            String replacement = StringUtils.hasText(replaceChar) ? replaceChar : SensitiveWordConstant.DEFAULT_REPLACE_CHAR;
            return sensitiveWordBs.replace(content, replacement);
        } catch (Exception e) {
            log.error("敏感词替换异常: {}", e.getMessage(), e);
            return content;
        }
    }
    
    @Override
    public boolean containsSensitiveWord(String content) {
        if (!StringUtils.hasText(content)) {
            return false;
        }
        
        try {
            return sensitiveWordBs.contains(content);
        } catch (Exception e) {
            log.error("敏感词检测异常: {}", e.getMessage(), e);
            return false;
        }
    }
    
    @Override
    public List<String> findSensitiveWords(String content) {
        if (!StringUtils.hasText(content)) {
            return Collections.emptyList();
        }
        
        try {
            return sensitiveWordBs.findAll(content);
        } catch (Exception e) {
            log.error("敏感词查找异常: {}", e.getMessage(), e);
            return Collections.emptyList();
        }
    }
    
    @Override
    public void refreshCache() {
        log.info("开始刷新敏感词库缓存");
        initSensitiveWords();
        log.info("敏感词库缓存刷新完成");
    }
    
    @Override
    public void initSensitiveWords() {
        try {
            log.info("开始初始化敏感词库");
            
            // 获取所有启用的敏感词
            List<SensitiveWordEntity> words = sensitiveWordService.findAllEnabled();
            List<SensitiveWordCategoryEntity> categories = categoryService.findAllEnabled();
            
            // 构建词汇映射
            wordEntityMap = words.stream()
                .collect(Collectors.toMap(SensitiveWordEntity::getWord, entity -> entity, (existing, replacement) -> existing));
            
            // 构建分类映射
            categoryEntityMap = categories.stream()
                .collect(Collectors.toMap(SensitiveWordCategoryEntity::getId, entity -> entity, (existing, replacement) -> existing));
            
            // 提取敏感词列表
            List<String> wordList = words.stream()
                .map(SensitiveWordEntity::getWord)
                .collect(Collectors.toList());
            
            // 初始化敏感词检测器
            sensitiveWordBs = SensitiveWordBs.newInstance()
                .wordDeny(new IWordDeny() {
                    @Override
                    public List<String> deny() {
                        return wordList;
                    }
                })
                .wordAllow(new IWordAllow() {
                    @Override
                    public List<String> allow() {
                        return Collections.emptyList(); // 暂时不设置白名单
                    }
                })
                .ignoreCase(true)
                .ignoreWidth(true)
                .ignoreNumStyle(true)
                .ignoreChineseStyle(true)
                .ignoreEnglishStyle(true)
                .ignoreRepeat(false)
                .enableNumCheck(true)
                .enableEmailCheck(true)
                .enableUrlCheck(true)
                .enableWordCheck(true)
                .numCheckLen(8)
                .init();
            
            log.info("敏感词库初始化完成，共加载 {} 个敏感词", wordList.size());
            
        } catch (Exception e) {
            log.error("初始化敏感词库失败: {}", e.getMessage(), e);
            // 初始化一个空的检测器，避免空指针异常
            sensitiveWordBs = SensitiveWordHelper.init();
        }
    }
}
