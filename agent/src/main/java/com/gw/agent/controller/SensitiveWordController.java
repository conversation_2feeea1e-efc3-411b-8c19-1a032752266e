package com.gw.agent.controller;

import com.github.pagehelper.PageInfo;
import com.gw.agent.dto.SensitiveWordCheckDTO;
import com.gw.agent.dto.SensitiveWordQueryDTO;
import com.gw.agent.dto.SensitiveWordSubmitDTO;
import com.gw.agent.dto.SensitiveWordUpdateDTO;
import com.gw.agent.entity.SensitiveWordEntity;
import com.gw.agent.service.SensitiveWordDetectionService;
import com.gw.agent.service.SensitiveWordService;
import com.gw.agent.vo.SensitiveWordCheckResultVO;
import com.gw.agent.vo.SensitiveWordVO;
import com.gw.common.dto.ItemIdDTO;

import com.gw.common.dto.PageBaseRequest;
import com.gw.common.dto.ResponseResult;

import com.gw.common.user.context.UserContextUtil;
import com.gw.common.vo.PageBaseContentVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

import static com.gw.common.exception.BusinessExceptionCode.FAIL_CODE;


/**
 * 敏感词控制器
 */
@RestController
@RequestMapping("/api/v1/sensitive-word")
@RequiredArgsConstructor
@Tag(name = "敏感词管理", description = "敏感词相关API")
@Log4j2
public class SensitiveWordController {
    
    private final SensitiveWordService sensitiveWordService;
    private final SensitiveWordDetectionService detectionService;
    
    /**
     * 创建敏感词
     */
    @Operation(summary = "创建敏感词", description = "创建一个新的敏感词")
    @PostMapping("")
    public ResponseResult<?> createSensitiveWord(@RequestBody @Valid SensitiveWordSubmitDTO req) {
        String username = UserContextUtil.getCurrentUsername();
        
        SensitiveWordEntity entity = new SensitiveWordEntity();
        BeanUtils.copyProperties(req, entity);
        entity.setCreator(username);
        entity.setUpdater(username);
        
        sensitiveWordService.insert(entity);
        
        // 刷新检测服务缓存
        detectionService.refreshCache();
        
        return ResponseResult.success(null);
    }
    
    /**
     * 更新敏感词
     */
    @Operation(summary = "更新敏感词", description = "更新敏感词信息")
    @PutMapping("")
    public ResponseResult<?> updateSensitiveWord(@RequestBody @Valid SensitiveWordUpdateDTO req) {
        String username = UserContextUtil.getCurrentUsername();
        
        SensitiveWordEntity entity = sensitiveWordService.findById(req.getId());
        if (entity == null) {
            return ResponseResult.failure(FAIL_CODE.getCode(), "敏感词不存在");
        }
        
        // 只更新非空字段
        if (req.getWord() != null) entity.setWord(req.getWord());
        if (req.getCategoryId() != null) entity.setCategoryId(req.getCategoryId());
        if (req.getLevel() != null) entity.setLevel(req.getLevel());
        if (req.getAction() != null) entity.setAction(req.getAction());
        if (req.getReplacement() != null) entity.setReplacement(req.getReplacement());
        if (req.getStatus() != null) entity.setStatus(req.getStatus());
        if (req.getRemark() != null) entity.setRemark(req.getRemark());
        
        entity.setUpdater(username);
        entity.setUpdateTime(LocalDateTime.now());
        
        sensitiveWordService.update(entity);
        
        // 刷新检测服务缓存
        detectionService.refreshCache();
        
        return ResponseResult.success(null);
    }
    
    /**
     * 删除敏感词
     */
    @Operation(summary = "删除敏感词", description = "删除指定的敏感词")
    @DeleteMapping("/{id}")
    public ResponseResult<?> deleteSensitiveWord(@PathVariable Long id) {
        SensitiveWordEntity entity = sensitiveWordService.findById(id);
        if (entity == null) {
            return ResponseResult.failure(FAIL_CODE.getCode(), "敏感词不存在");
        }
        
        sensitiveWordService.delete(id);
        
        // 刷新检测服务缓存
        detectionService.refreshCache();
        
        return ResponseResult.success(null);
    }
    
    /**
     * 获取敏感词详情
     */
    @Operation(summary = "获取敏感词详情", description = "获取敏感词详细信息")
    @PostMapping("/get")
    public ResponseResult<SensitiveWordVO> getSensitiveWord(@RequestBody @Valid ItemIdDTO req) {
        SensitiveWordEntity entity = sensitiveWordService.findById(req.getId());
        if (entity == null) {
            return ResponseResult.failure(FAIL_CODE.getCode(), "敏感词不存在");
        }
        
        return ResponseResult.success(new SensitiveWordVO(entity));
    }
    
    /**
     * 分页查询敏感词
     */
    @Operation(summary = "分页查询敏感词", description = "分页获取敏感词列表")
    @PostMapping("/page")
    public ResponseResult<PageBaseContentVo<SensitiveWordVO>> pageSensitiveWords(
            @RequestBody @Valid PageBaseRequest<SensitiveWordQueryDTO> params) {
        
        if (params.getFilter() == null) {
            params.setFilter(new SensitiveWordQueryDTO());
        }
        
        PageInfo<SensitiveWordEntity> pageInfo = sensitiveWordService.page(
                params.getCurrent(), params.getPageSize(), params.getFilter());
        
        List<SensitiveWordVO> vos = pageInfo.getList().stream()
            .map(SensitiveWordVO::new)
            .collect(Collectors.toList());
        
        PageBaseContentVo<SensitiveWordVO> result = new PageBaseContentVo<>();
        result.setList(vos);
        result.setTotal(pageInfo.getTotal());
        result.setPageNum(pageInfo.getPageNum());
        result.setPageSize(pageInfo.getPageSize());
        result.setPages(pageInfo.getPages());
        
        return ResponseResult.success(result);
    }
    
    /**
     * 启用/禁用敏感词
     */
    @Operation(summary = "启用/禁用敏感词", description = "更新敏感词的启用状态")
    @PutMapping("/{id}/status/{status}")
    public ResponseResult<?> updateSensitiveWordStatus(@PathVariable Long id, @PathVariable Integer status) {
        SensitiveWordEntity entity = sensitiveWordService.findById(id);
        if (entity == null) {
            return ResponseResult.failure(FAIL_CODE.getCode(), "敏感词不存在");
        }
        
        sensitiveWordService.updateStatus(id, status);
        
        // 刷新检测服务缓存
        detectionService.refreshCache();
        
        return ResponseResult.success(null);
    }
    
    /**
     * 批量删除敏感词
     */
    @Operation(summary = "批量删除敏感词", description = "批量删除指定的敏感词")
    @PostMapping("/batch/delete")
    public ResponseResult<?> batchDeleteSensitiveWords(@RequestBody List<Long> ids) {
        sensitiveWordService.batchDelete(ids);
        
        // 刷新检测服务缓存
        detectionService.refreshCache();
        
        return ResponseResult.success(null);
    }
    
    /**
     * 检测文本中的敏感词
     */
    @Operation(summary = "检测文本敏感词", description = "检测文本中是否包含敏感词")
    @PostMapping("/check")
    public ResponseResult<SensitiveWordCheckResultVO> checkText(@RequestBody @Valid SensitiveWordCheckDTO req) {
        SensitiveWordCheckResultVO result = detectionService.checkText(req.getContent(), req.getDetailed());
        
        // 如果需要自动替换
        if (req.getAutoReplace() && result.getHasSensitiveWord()) {
            String processedText = detectionService.replaceText(req.getContent(), req.getReplaceChar());
            result.setProcessedText(processedText);
        }
        
        return ResponseResult.success(result);
    }
    
    /**
     * 替换文本中的敏感词
     */
    @Operation(summary = "替换文本敏感词", description = "替换文本中的敏感词")
    @PostMapping("/replace")
    public ResponseResult<String> replaceText(@RequestBody @Valid SensitiveWordCheckDTO req) {
        String result = detectionService.replaceText(req.getContent(), req.getReplaceChar());
        return ResponseResult.success(result);
    }
    
    /**
     * 刷新敏感词库缓存
     */
    @Operation(summary = "刷新敏感词库缓存", description = "刷新敏感词检测服务的缓存")
    @PostMapping("/cache/refresh")
    public ResponseResult<?> refreshCache() {
        detectionService.refreshCache();
        return ResponseResult.success(null);
    }
    
    /**
     * 清除敏感词缓存
     */
    @Operation(summary = "清除敏感词缓存", description = "清除敏感词相关缓存")
    @PostMapping("/cache/clear")
    public ResponseResult<?> clearCache() {
        sensitiveWordService.clearCache();
        return ResponseResult.success(null);
    }
}
