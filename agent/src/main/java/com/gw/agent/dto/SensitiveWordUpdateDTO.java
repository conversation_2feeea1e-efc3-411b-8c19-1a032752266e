package com.gw.agent.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.Data;

/**
 * 敏感词更新参数
 */
@Data
@Schema(description = "敏感词更新参数")
public class SensitiveWordUpdateDTO {
    
    @Schema(description = "敏感词ID")
    @NotNull(message = "敏感词ID不能为空")
    @Positive(message = "敏感词ID必须为正数")
    private Long id;
    
    @Schema(description = "敏感词内容")
    private String word;
    
    @Schema(description = "分类ID")
    private Long categoryId;
    
    @Schema(description = "敏感词级别：1-低级，2-中级，3-高级，4-严重")
    private Integer level;
    
    @Schema(description = "处理动作：1-拦截，2-替换，3-警告")
    private Integer action;
    
    @Schema(description = "替换内容（当action为2时使用）")
    private String replacement;
    
    @Schema(description = "状态：0-禁用，1-启用")
    private Integer status;
    
    @Schema(description = "备注")
    private String remark;
}
