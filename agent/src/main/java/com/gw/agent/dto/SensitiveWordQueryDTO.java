package com.gw.agent.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 敏感词查询参数
 */
@Data
@Schema(description = "敏感词查询参数")
public class SensitiveWordQueryDTO {
    
    @Schema(description = "敏感词内容（模糊查询）")
    private String word;
    
    @Schema(description = "分类ID")
    private Long categoryId;
    
    @Schema(description = "敏感词级别：1-低级，2-中级，3-高级，4-严重")
    private Integer level;
    
    @Schema(description = "处理动作：1-拦截，2-替换，3-警告")
    private Integer action;
    
    @Schema(description = "状态：0-禁用，1-启用")
    private Integer status;
    
    @Schema(description = "创建者")
    private String creator;
    
    @Schema(description = "关键词搜索")
    private String keyword;
    
    @Schema(description = "排序字段")
    private String sortField;
    
    @Schema(description = "排序方式：asc-升序，desc-降序")
    private String sortOrder;
}
