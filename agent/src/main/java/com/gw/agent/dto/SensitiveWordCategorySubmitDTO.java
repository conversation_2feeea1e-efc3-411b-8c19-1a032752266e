package com.gw.agent.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 敏感词分类提交参数
 */
@Data
@Schema(description = "敏感词分类提交参数")
public class SensitiveWordCategorySubmitDTO {
    
    @Schema(description = "分类名称")
    @NotBlank(message = "分类名称不能为空")
    private String name;
    
    @Schema(description = "分类描述")
    private String description;
    
    @Schema(description = "状态：0-禁用，1-启用")
    @NotNull(message = "状态不能为空")
    private Integer status;
    
    @Schema(description = "排序序号")
    private Integer sequence = 0;
}
