package com.gw.agent.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.gw.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 敏感词分类实体
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_sensitive_word_category")
public class SensitiveWordCategoryEntity extends BaseEntity {
    
    /**
     * 分类名称
     */
    private String name;
    
    /**
     * 分类描述
     */
    private String description;
    
    /**
     * 分类状态：0-禁用，1-启用
     */
    private Integer status;
    
    /**
     * 排序序号
     */
    private Integer sequence;
    
    /**
     * 使用次数
     */
    private Integer useCount = 0;
}
