package com.gw.agent.vo;

import com.gw.agent.entity.SensitiveWordCategoryEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

import java.time.LocalDateTime;

/**
 * 敏感词分类视图对象
 */
@Data
@Schema(description = "敏感词分类VO")
@AllArgsConstructor
@NoArgsConstructor
public class SensitiveWordCategoryVO {
    
    @Schema(description = "分类ID")
    private Long id;
    
    @Schema(description = "分类名称")
    private String name;
    
    @Schema(description = "分类描述")
    private String description;
    
    @Schema(description = "状态：0-禁用，1-启用")
    private Integer status;
    
    @Schema(description = "排序序号")
    private Integer sequence;
    
    @Schema(description = "使用次数")
    private Integer useCount;
    
    @Schema(description = "创建者")
    private String creator;
    
    @Schema(description = "创建时间")
    private LocalDateTime createTime;
    
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;
    
    public SensitiveWordCategoryVO(SensitiveWordCategoryEntity entity) {
        BeanUtils.copyProperties(entity, this);
    }
}
