# 敏感词检测配置
sensitive-word:
  # 是否启用敏感词检测
  enabled: true
  
  # 默认替换字符
  default-replace-char: "*"
  
  # 检测配置
  ignore-case: true              # 是否忽略大小写
  ignore-width: true             # 是否忽略全角半角
  ignore-num-style: true         # 是否忽略数字样式
  ignore-chinese-style: true     # 是否忽略中文样式
  ignore-english-style: true     # 是否忽略英文样式
  ignore-repeat: false           # 是否忽略重复字符
  
  # 检测功能开关
  enable-num-check: true         # 是否启用数字检测
  enable-email-check: true       # 是否启用邮箱检测
  enable-url-check: true         # 是否启用URL检测
  enable-word-check: true        # 是否启用词汇检测
  
  # 检测参数
  num-check-len: 8              # 数字检测长度
  
  # 缓存配置
  cache-refresh-interval: 60     # 缓存刷新间隔（分钟）
  enable-cache: true            # 是否启用缓存
  cache-expire-time: 30         # 缓存过期时间（分钟）

# Spring Cache 配置
spring:
  cache:
    type: caffeine
    caffeine:
      spec: maximumSize=1000,expireAfterWrite=30m
    cache-names:
      - sensitiveWord
      - sensitiveWordCategory
      - sensitiveWordPage

# 日志配置
logging:
  level:
    com.gw.agent.service.impl.SensitiveWordDetectionServiceImpl: INFO
    com.gw.agent.service.impl.SensitiveWordServiceImpl: INFO
    com.gw.agent.service.impl.SensitiveWordCategoryServiceImpl: INFO
