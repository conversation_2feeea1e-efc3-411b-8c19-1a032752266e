<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gw.agent.mapper.sql.SensitiveWordMapper">
    
    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="com.gw.agent.entity.SensitiveWordEntity">
        <id column="id" property="id"/>
        <result column="creator" property="creator"/>
        <result column="updater" property="updater"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="deleted" property="deleted"/>
        <result column="word" property="word"/>
        <result column="category_id" property="categoryId"/>
        <result column="level" property="level"/>
        <result column="action" property="action"/>
        <result column="replacement" property="replacement"/>
        <result column="status" property="status"/>
        <result column="remark" property="remark"/>
        <result column="use_count" property="useCount"/>
    </resultMap>
    
    <!-- 分页查询敏感词 -->
    <select id="page" resultMap="BaseResultMap">
        SELECT sw.*, swc.name as category_name
        FROM t_sensitive_word sw
        LEFT JOIN t_sensitive_word_category swc ON sw.category_id = swc.id
        <where>
            sw.deleted = 0
            <if test="query.word != null and query.word != ''">
                AND sw.word LIKE CONCAT('%', #{query.word}, '%')
            </if>
            <if test="query.categoryId != null">
                AND sw.category_id = #{query.categoryId}
            </if>
            <if test="query.level != null">
                AND sw.level = #{query.level}
            </if>
            <if test="query.action != null">
                AND sw.action = #{query.action}
            </if>
            <if test="query.status != null">
                AND sw.status = #{query.status}
            </if>
            <if test="query.creator != null and query.creator != ''">
                AND sw.creator = #{query.creator}
            </if>
            <if test="query.keyword != null and query.keyword != ''">
                AND (sw.word LIKE CONCAT('%', #{query.keyword}, '%')
                OR sw.remark LIKE CONCAT('%', #{query.keyword}, '%')
                OR swc.name LIKE CONCAT('%', #{query.keyword}, '%'))
            </if>
        </where>
        <choose>
            <when test="query.sortField != null and query.sortField != ''">
                ORDER BY 
                <choose>
                    <when test="query.sortField == 'createTime'">sw.create_time</when>
                    <when test="query.sortField == 'updateTime'">sw.update_time</when>
                    <when test="query.sortField == 'useCount'">sw.use_count</when>
                    <when test="query.sortField == 'level'">sw.level</when>
                    <otherwise>sw.create_time</otherwise>
                </choose>
                <choose>
                    <when test="query.sortOrder != null and query.sortOrder.toLowerCase() == 'asc'">ASC</when>
                    <otherwise>DESC</otherwise>
                </choose>
            </when>
            <otherwise>
                ORDER BY sw.create_time DESC
            </otherwise>
        </choose>
    </select>
    
</mapper>
