# 政治敏感词SQL文件说明

## 文件概述

本目录包含了政治敏感词相关的SQL文件，用于向敏感词检测系统中批量导入政治敏感词汇。

## 文件列表

### 1. `sw_政治.txt`
- **描述**: 原始政治敏感词文本文件
- **格式**: 每行一个敏感词，以逗号分隔
- **数量**: 1093个敏感词
- **来源**: 政治敏感词汇库

### 2. `political_sensitive_words_complete.sql`
- **描述**: 完整的政治敏感词SQL插入语句
- **格式**: 标准SQL INSERT语句
- **分类**: 政治敏感 (category_id = 1)
- **自动生成**: 基于 `sw_政治.txt` 文件自动生成

### 3. `insert_political_sensitive_words.sql`
- **描述**: 手工编写的部分政治敏感词SQL示例
- **用途**: 演示和测试用途

### 4. `generate_political_words_sql.py`
- **描述**: Python脚本，用于自动生成SQL文件
- **功能**: 读取文本文件并生成标准SQL插入语句

## 敏感词分级规则

### 严重级别 (level=4, action=1-拦截)
- **数量**: 30个
- **处理**: 直接拦截，不允许发布
- **关键词**: 包含"独立"、"分裂"、"颠覆"、"推翻"、"暴动"、"革命"、"专制"等
- **示例**: "台湾独立"、"共产专制"、"出现暴动"

### 高级别 (level=3, action=1-拦截)
- **数量**: 414个
- **处理**: 拦截，需要人工审核
- **关键词**: 政治人物姓名、政治组织、敏感政治词汇
- **示例**: "江泽民"、"胡锦涛"、"异议人士"

### 中级别 (level=2, action=2-替换)
- **数量**: 649个
- **处理**: 自动替换为"***"
- **关键词**: 一般政治词汇、政府机构等
- **示例**: "政腐"、"症腐"、"5毛党"

### 低级别 (level=1, action=3-警告)
- **数量**: 0个
- **处理**: 仅警告，允许发布
- **说明**: 当前政治敏感词库中无低级别词汇

## 使用方法

### 1. 执行SQL文件
```sql
-- 首先确保敏感词分类表中存在政治敏感分类
-- 然后执行完整的SQL文件
\i political_sensitive_words_complete.sql
```

### 2. 验证导入结果
```sql
-- 查询政治敏感词总数
SELECT COUNT(*) FROM t_sensitive_word WHERE category_id = 1;

-- 按级别统计
SELECT level, COUNT(*) as count 
FROM t_sensitive_word 
WHERE category_id = 1 
GROUP BY level 
ORDER BY level;

-- 查看部分敏感词
SELECT word, level, action, replacement 
FROM t_sensitive_word 
WHERE category_id = 1 
LIMIT 10;
```

### 3. 测试敏感词检测
```java
// 使用敏感词检测服务测试
String testText = "这段文本包含政治敏感词";
SensitiveWordCheckResultVO result = detectionService.checkText(testText, true);
```

## 注意事项

1. **数据库兼容性**: SQL语句使用标准SQL语法，兼容PostgreSQL、MySQL等主流数据库
2. **字符编码**: 文件使用UTF-8编码，确保中文字符正确显示
3. **批量插入**: SQL文件按50个词汇一批进行分组，便于执行和调试
4. **重复检查**: 数据库表设计包含唯一索引，防止重复插入
5. **性能考虑**: 大量数据插入时建议分批执行，避免长时间锁表

## 维护说明

### 添加新的敏感词
1. 将新词汇添加到 `sw_政治.txt` 文件
2. 运行 `generate_political_words_sql.py` 脚本重新生成SQL文件
3. 执行新生成的SQL文件

### 修改分级规则
1. 编辑 `generate_political_words_sql.py` 脚本中的 `determine_level_and_action` 函数
2. 重新生成SQL文件
3. 清空现有数据后重新导入

### 自定义处理动作
- **action=1**: 拦截，完全禁止
- **action=2**: 替换，自动替换为指定字符
- **action=3**: 警告，记录但允许通过

## 技术支持

如有问题或需要定制，请联系开发团队。

---
*最后更新时间: 2025-07-26*
*敏感词数量: 1093个*
*文件版本: v1.0*
