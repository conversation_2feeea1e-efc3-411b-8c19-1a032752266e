#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
政治敏感词SQL生成脚本
读取 sw_政治.txt 文件，生成标准的SQL插入语句
"""

import re
import os

def clean_word(word):
    """清理敏感词，去除多余字符"""
    word = word.strip()
    word = word.rstrip(',')
    word = word.rstrip('.')
    word = word.rstrip('。')
    return word

def determine_level_and_action(word):
    """根据敏感词内容确定级别和处理动作"""
    # 严重级别关键词 (level=4, action=1-拦截)
    severe_keywords = [
        '独立', '分裂', '颠覆', '推翻', '暴动', '革命', '专制', '民贼', 
        '灭亡', '解体', '焚烧', '严重', '封锁', '暴力', '恐怖', '极端'
    ]
    
    # 高级别关键词 (level=3, action=1-拦截)
    high_keywords = [
        '江', '胡', '温', '习', '李', '周', '政治', '党派', '组织', 
        '异议', '抗议', '示威', '罢', '集会', '游行', '宪政'
    ]
    
    # 检查是否包含严重级别关键词
    for keyword in severe_keywords:
        if keyword in word:
            return 4, 1, None  # 严重级别，拦截
    
    # 检查是否包含高级别关键词
    for keyword in high_keywords:
        if keyword in word:
            return 3, 1, None  # 高级别，拦截
    
    # 检查是否是人名（通常包含姓氏）
    chinese_surnames = ['李', '王', '张', '刘', '陈', '杨', '赵', '黄', '周', '吴', '徐', '孙', '胡', '朱', '高', '林', '何', '郭', '马', '罗', '梁', '宋', '郑', '谢', '韩', '唐', '冯', '于', '董', '萧', '程', '曹', '袁', '邓', '许', '傅', '沈', '曾', '彭', '吕', '苏', '卢', '蒋', '蔡', '贾', '丁', '魏', '薛', '叶', '阎', '余', '潘', '杜', '戴', '夏', '钟', '汪', '田', '任', '姜', '范', '方', '石', '姚', '谭', '廖', '邹', '熊', '金', '陆', '郝', '孔', '白', '崔', '康', '毛', '邱', '秦', '江', '史', '顾', '侯', '邵', '孟', '龙', '万', '段', '雷', '钱', '汤', '尹', '黎', '易', '常', '武', '乔', '贺', '赖', '龚', '文']
    
    for surname in chinese_surnames:
        if word.startswith(surname) and len(word) >= 2:
            return 3, 1, None  # 人名，高级别，拦截
    
    # 默认中级别，替换
    return 2, 2, '***'

def generate_sql():
    """生成SQL插入语句"""
    input_file = 'agent/src/main/resources/sw_政治.txt'
    output_file = 'agent/src/main/resources/political_sensitive_words_complete.sql'
    
    if not os.path.exists(input_file):
        print(f"输入文件不存在: {input_file}")
        return
    
    words = []
    
    # 读取敏感词文件
    with open(input_file, 'r', encoding='utf-8') as f:
        for line in f:
            word = clean_word(line)
            if word and word != '':
                words.append(word)
    
    print(f"读取到 {len(words)} 个敏感词")
    
    # 生成SQL语句
    sql_statements = []
    sql_statements.append("-- 政治敏感词完整插入SQL语句")
    sql_statements.append("-- 基于 sw_政治.txt 文件自动生成")
    sql_statements.append("-- 分类ID: 1 (政治敏感)")
    sql_statements.append("-- 级别说明: 1-低级，2-中级，3-高级，4-严重")
    sql_statements.append("-- 动作说明: 1-拦截，2-替换，3-警告")
    sql_statements.append("")
    
    # 分批插入，每50个词一批
    batch_size = 50
    for i in range(0, len(words), batch_size):
        batch = words[i:i+batch_size]
        
        sql_statements.append(f"-- 批次 {i//batch_size + 1}: 插入第 {i+1} 到第 {min(i+batch_size, len(words))} 个敏感词")
        sql_statements.append("INSERT INTO t_sensitive_word (word, category_id, level, action, replacement, status, remark, creator, updater, create_time, update_time, deleted, use_count) VALUES")
        
        values = []
        for j, word in enumerate(batch):
            level, action, replacement = determine_level_and_action(word)
            
            # 转义单引号
            escaped_word = word.replace("'", "''")
            
            # 构建replacement字段
            replacement_str = f"'{replacement}'" if replacement else "NULL"
            
            # 确定备注
            if level == 4:
                remark = "政治敏感词-严重级别"
            elif level == 3:
                remark = "政治敏感词-高级别"
            elif level == 2:
                remark = "政治敏感词-中级别"
            else:
                remark = "政治敏感词-低级别"
            
            value = f"('{escaped_word}', 1, {level}, {action}, {replacement_str}, 1, '{remark}', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 0, 0)"
            values.append(value)
        
        sql_statements.append(",\n".join(values) + ";")
        sql_statements.append("")
    
    # 添加统计信息
    level_counts = {1: 0, 2: 0, 3: 0, 4: 0}
    for word in words:
        level, _, _ = determine_level_and_action(word)
        level_counts[level] += 1
    
    sql_statements.append("-- 统计信息:")
    sql_statements.append(f"-- 总计敏感词数量: {len(words)} 个")
    sql_statements.append(f"-- 低级别(level=1): {level_counts[1]} 个")
    sql_statements.append(f"-- 中级别(level=2): {level_counts[2]} 个")
    sql_statements.append(f"-- 高级别(level=3): {level_counts[3]} 个")
    sql_statements.append(f"-- 严重级别(level=4): {level_counts[4]} 个")
    
    # 写入输出文件
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write('\n'.join(sql_statements))
    
    print(f"SQL文件已生成: {output_file}")
    print(f"统计信息:")
    print(f"  总计: {len(words)} 个敏感词")
    print(f"  低级别: {level_counts[1]} 个")
    print(f"  中级别: {level_counts[2]} 个")
    print(f"  高级别: {level_counts[3]} 个")
    print(f"  严重级别: {level_counts[4]} 个")

if __name__ == "__main__":
    generate_sql()
