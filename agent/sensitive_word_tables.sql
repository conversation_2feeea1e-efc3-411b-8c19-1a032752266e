-- 敏感词分类表
CREATE TABLE IF NOT EXISTS t_sensitive_word_category (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL COMMENT '分类名称',
    description TEXT COMMENT '分类描述',
    status INTEGER NOT NULL DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
    sequence INTEGER NOT NULL DEFAULT 0 COMMENT '排序序号',
    use_count INTEGER NOT NULL DEFAULT 0 COMMENT '使用次数',
    creator VARCHAR(100) COMMENT '创建者',
    updater VARCHAR(100) COMMENT '更新者',
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted INTEGER NOT NULL DEFAULT 0 COMMENT '删除标记：0-未删除，1-已删除'
);

-- 创建分类表索引
CREATE INDEX idx_sensitive_word_category_name ON t_sensitive_word_category(name);
CREATE INDEX idx_sensitive_word_category_status ON t_sensitive_word_category(status);
CREATE INDEX idx_sensitive_word_category_deleted ON t_sensitive_word_category(deleted);

-- 敏感词表
CREATE TABLE IF NOT EXISTS t_sensitive_word (
    id BIGSERIAL PRIMARY KEY,
    word VARCHAR(500) NOT NULL COMMENT '敏感词内容',
    category_id BIGINT COMMENT '分类ID',
    level INTEGER NOT NULL DEFAULT 1 COMMENT '敏感词级别：1-低级，2-中级，3-高级，4-严重',
    action INTEGER NOT NULL DEFAULT 1 COMMENT '处理动作：1-拦截，2-替换，3-警告',
    replacement VARCHAR(500) COMMENT '替换内容（当action为2时使用）',
    status INTEGER NOT NULL DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
    remark TEXT COMMENT '备注',
    use_count INTEGER NOT NULL DEFAULT 0 COMMENT '使用次数',
    creator VARCHAR(100) COMMENT '创建者',
    updater VARCHAR(100) COMMENT '更新者',
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted INTEGER NOT NULL DEFAULT 0 COMMENT '删除标记：0-未删除，1-已删除',
    FOREIGN KEY (category_id) REFERENCES t_sensitive_word_category(id)
);

-- 创建敏感词表索引
CREATE INDEX idx_sensitive_word_word ON t_sensitive_word(word);
CREATE INDEX idx_sensitive_word_category_id ON t_sensitive_word(category_id);
CREATE INDEX idx_sensitive_word_level ON t_sensitive_word(level);
CREATE INDEX idx_sensitive_word_action ON t_sensitive_word(action);
CREATE INDEX idx_sensitive_word_status ON t_sensitive_word(status);
CREATE INDEX idx_sensitive_word_deleted ON t_sensitive_word(deleted);
CREATE INDEX idx_sensitive_word_creator ON t_sensitive_word(creator);
CREATE INDEX idx_sensitive_word_create_time ON t_sensitive_word(create_time);

-- 创建唯一索引确保敏感词不重复
CREATE UNIQUE INDEX uk_sensitive_word_word_deleted ON t_sensitive_word(word, deleted);

-- 插入默认分类数据
INSERT INTO t_sensitive_word_category (name, description, status, sequence, creator, updater) VALUES
('政治敏感', '涉及政治相关的敏感词汇', 1, 1, 'system', 'system'),
('色情低俗', '涉及色情、低俗内容的敏感词汇', 1, 2, 'system', 'system'),
('暴力血腥', '涉及暴力、血腥内容的敏感词汇', 1, 3, 'system', 'system'),
('违法犯罪', '涉及违法犯罪活动的敏感词汇', 1, 4, 'system', 'system'),
('赌博诈骗', '涉及赌博、诈骗等非法活动的敏感词汇', 1, 5, 'system', 'system'),
('广告营销', '涉及广告营销、推广的敏感词汇', 1, 6, 'system', 'system'),
('其他', '其他类型的敏感词汇', 1, 99, 'system', 'system');

-- 插入一些示例敏感词数据
INSERT INTO t_sensitive_word (word, category_id, level, action, status, remark, creator, updater) VALUES
('测试敏感词1', 7, 1, 2, 1, '测试用敏感词', 'system', 'system'),
('测试敏感词2', 7, 2, 1, 1, '测试用敏感词', 'system', 'system'),
('测试敏感词3', 7, 3, 3, 1, '测试用敏感词', 'system', 'system');

-- 添加表注释
COMMENT ON TABLE t_sensitive_word_category IS '敏感词分类表';
COMMENT ON TABLE t_sensitive_word IS '敏感词表';
