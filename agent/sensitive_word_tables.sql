-- 敏感词分类表
-- 删除已存在的表和序列
DROP TABLE IF EXISTS "public"."t_sensitive_word_category";
DROP SEQUENCE IF EXISTS t_sensitive_word_category_id_seq;

-- 创建序列
CREATE SEQUENCE t_sensitive_word_category_id_seq START 1 CACHE 1;

-- 创建表
CREATE TABLE t_sensitive_word_category
(
    -- BaseEntity字段
    id          BIGINT PRIMARY KEY DEFAULT NEXTVAL('t_sensitive_word_category_id_seq'::regclass),
    create_time TIMESTAMP          DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP          DEFAULT CURRENT_TIMESTAMP,
    deleted     INTEGER            DEFAULT 0,
    creator     VARCHA<PERSON>(100),
    updater     VARCHAR(100),

    -- SensitiveWordCategoryEntity特有字段
    name        VARCHAR(100) NOT NULL,
    description TEXT,
    status      INTEGER      NOT NULL DEFAULT 1,
    sequence    INTEGER      NOT NULL DEFAULT 0,
    use_count   INTEGER      NOT NULL DEFAULT 0
);

-- 添加表注释
COMMENT ON TABLE t_sensitive_word_category IS '敏感词分类表';
COMMENT ON COLUMN t_sensitive_word_category.id IS '主键ID';
COMMENT ON COLUMN t_sensitive_word_category.create_time IS '创建时间';
COMMENT ON COLUMN t_sensitive_word_category.update_time IS '更新时间';
COMMENT ON COLUMN t_sensitive_word_category.deleted IS '删除标记(0:未删除,1:已删除)';
COMMENT ON COLUMN t_sensitive_word_category.creator IS '创建者';
COMMENT ON COLUMN t_sensitive_word_category.updater IS '更新者';
COMMENT ON COLUMN t_sensitive_word_category.name IS '分类名称';
COMMENT ON COLUMN t_sensitive_word_category.description IS '分类描述';
COMMENT ON COLUMN t_sensitive_word_category.status IS '状态：0-禁用，1-启用';
COMMENT ON COLUMN t_sensitive_word_category.sequence IS '排序序号';
COMMENT ON COLUMN t_sensitive_word_category.use_count IS '使用次数';

-- 创建分类表索引
CREATE INDEX idx_sensitive_word_category_name ON t_sensitive_word_category(name);
CREATE INDEX idx_sensitive_word_category_status ON t_sensitive_word_category(status);
CREATE INDEX idx_sensitive_word_category_deleted ON t_sensitive_word_category(deleted);

-- 敏感词表
-- 删除已存在的表和序列
DROP TABLE IF EXISTS "public"."t_sensitive_word";
DROP SEQUENCE IF EXISTS t_sensitive_word_id_seq;

-- 创建序列
CREATE SEQUENCE t_sensitive_word_id_seq START 1 CACHE 1;

-- 创建表
CREATE TABLE t_sensitive_word
(
    -- BaseEntity字段
    id          BIGINT PRIMARY KEY DEFAULT NEXTVAL('t_sensitive_word_id_seq'::regclass),
    create_time TIMESTAMP          DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP          DEFAULT CURRENT_TIMESTAMP,
    deleted     INTEGER            DEFAULT 0,
    creator     VARCHAR(100),
    updater     VARCHAR(100),

    -- SensitiveWordEntity特有字段
    word         VARCHAR(500) NOT NULL,
    category_id  BIGINT,
    level        INTEGER      NOT NULL DEFAULT 1,
    action       INTEGER      NOT NULL DEFAULT 1,
    replacement  VARCHAR(500),
    status       INTEGER      NOT NULL DEFAULT 1,
    remark       TEXT,
    use_count    INTEGER      NOT NULL DEFAULT 0,
    
    FOREIGN KEY (category_id) REFERENCES t_sensitive_word_category(id)
);

-- 添加表注释
COMMENT ON TABLE t_sensitive_word IS '敏感词表';
COMMENT ON COLUMN t_sensitive_word.id IS '主键ID';
COMMENT ON COLUMN t_sensitive_word.create_time IS '创建时间';
COMMENT ON COLUMN t_sensitive_word.update_time IS '更新时间';
COMMENT ON COLUMN t_sensitive_word.deleted IS '删除标记(0:未删除,1:已删除)';
COMMENT ON COLUMN t_sensitive_word.creator IS '创建者';
COMMENT ON COLUMN t_sensitive_word.updater IS '更新者';
COMMENT ON COLUMN t_sensitive_word.word IS '敏感词内容';
COMMENT ON COLUMN t_sensitive_word.category_id IS '分类ID';
COMMENT ON COLUMN t_sensitive_word.level IS '敏感词级别：1-低级，2-中级，3-高级，4-严重';
COMMENT ON COLUMN t_sensitive_word.action IS '处理动作：1-拦截，2-替换，3-警告';
COMMENT ON COLUMN t_sensitive_word.replacement IS '替换内容（当action为2时使用）';
COMMENT ON COLUMN t_sensitive_word.status IS '状态：0-禁用，1-启用';
COMMENT ON COLUMN t_sensitive_word.remark IS '备注';
COMMENT ON COLUMN t_sensitive_word.use_count IS '使用次数';

-- 创建敏感词表索引
CREATE INDEX idx_sensitive_word_word ON t_sensitive_word(word);
CREATE INDEX idx_sensitive_word_category_id ON t_sensitive_word(category_id);
CREATE INDEX idx_sensitive_word_level ON t_sensitive_word(level);
CREATE INDEX idx_sensitive_word_action ON t_sensitive_word(action);
CREATE INDEX idx_sensitive_word_status ON t_sensitive_word(status);
CREATE INDEX idx_sensitive_word_deleted ON t_sensitive_word(deleted);
CREATE INDEX idx_sensitive_word_creator ON t_sensitive_word(creator);
CREATE INDEX idx_sensitive_word_create_time ON t_sensitive_word(create_time);

-- 创建唯一索引确保敏感词不重复
CREATE UNIQUE INDEX uk_sensitive_word_word_deleted ON t_sensitive_word(word, deleted);

